#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信RPC连接测试脚本
用于验证微信RPC服务是否正常运行
"""

import requests
import json

# 配置信息
RPC_HOST = "http://127.0.0.1:50007"
WECHAT_PID = "22504"

def test_wechat_list():
    """测试获取微信客户端列表"""
    print("测试1: 获取微信客户端列表")
    try:
        response = requests.get(f"{RPC_HOST}/api/wechat/list")
        if response.status_code == 200:
            data = response.json()
            print(f"响应状态: {data.get('Code')}")
            print(f"响应消息: {data.get('Msg')}")
            if data.get('Data'):
                for client in data['Data']:
                    print(f"  PID: {client.get('Pid')}, 路径: {client.get('Path')}")
            print("✓ 微信客户端列表获取成功")
        else:
            print(f"✗ 请求失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 连接异常: {e}")
    print("-" * 50)

def test_db_info():
    """测试获取数据库信息"""
    print("测试2: 获取数据库信息")
    try:
        headers = {
            "X-WeChat-PID": WECHAT_PID,
            "Content-Type": "application/json"
        }
        
        response = requests.post(
            f"{RPC_HOST}/api/db/getDBInfo",
            headers=headers,
            json={}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应状态: {data.get('Code')}")
            print(f"响应消息: {data.get('Msg')}")
            if data.get('Data'):
                for db in data['Data']:
                    print(f"  数据库: {db.get('databaseName')}")
                    print(f"  句柄: {db.get('handle')}")
                    print(f"  表数量: {db.get('tables', 0)}")
            print("✓ 数据库信息获取成功")
        else:
            print(f"✗ 请求失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 连接异常: {e}")
    print("-" * 50)

def test_user_info():
    """测试获取当前用户信息"""
    print("测试3: 获取当前用户信息")
    try:
        headers = {
            "X-WeChat-PID": WECHAT_PID,
            "Content-Type": "application/json"
        }
        
        response = requests.post(
            f"{RPC_HOST}/api/user/userInfo",
            headers=headers,
            json={}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应状态: {data.get('Code')}")
            print(f"响应消息: {data.get('Msg')}")
            if data.get('Data'):
                user_info = data['Data']
                print(f"  微信号: {user_info.get('wxid')}")
                print(f"  昵称: {user_info.get('nickname')}")
                print(f"  手机号: {user_info.get('mobile')}")
            print("✓ 用户信息获取成功")
        else:
            print(f"✗ 请求失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 连接异常: {e}")
    print("-" * 50)

def main():
    """主测试函数"""
    print("微信RPC连接测试开始")
    print("=" * 50)
    
    # 测试基本连接
    test_wechat_list()
    
    # 测试数据库连接
    test_db_info()
    
    # 测试用户信息
    test_user_info()
    
    print("测试完成")

if __name__ == "__main__":
    main()
