# 微信群消息监控系统 - 完整技术文档

## 项目概述

本项目实现了一个完整的微信群消息监控系统，能够实时监控所有微信群聊的新消息，并以JSON格式输出包含真实发送人昵称和引用消息信息的完整数据。

## 核心功能

- ✅ **实时监控**：监控获取全部群的新消息
- ✅ **真实昵称**：显示发送人真实昵称（而非微信ID）
- ✅ **引用消息识别**：识别和解析引用回复消息
- ✅ **统一ID体系**：全部使用MsgSvrID保证全局唯一性
- ✅ **JSON格式输出**：标准化的数据输出格式

## 技术架构

### 1. 基础环境
- **微信RPC服务**：运行在 `http://127.0.0.1:50007`
- **微信进程ID**：`22504`
- **数据库访问**：通过RPC接口访问微信本地SQLite数据库

### 2. 核心数据库
- **MSG0.db**：存储消息数据
- **MicroMsg.db**：存储联系人和群聊信息

### 3. 关键数据表结构

#### MSG表（MSG0.db）
```sql
CREATE TABLE MSG (
    localId INTEGER,        -- 本地消息ID（用于增量查询）
    MsgSvrID TEXT,         -- 服务器消息ID（全局唯一标识符）
    CreateTime INT,        -- 创建时间戳
    
    StrTalker TEXT,        -- 会话标识（群聊以@chatroom结尾）
    StrContent TEXT,       -- 消息内容
    IsSender INT,          -- 是否自己发送（1=是，0=否）
    Type INT,              -- 消息类型（1=文本，49=特殊消息）
    SubType INT,           -- 消息子类型
    BytesExtra BLOB,       -- 额外信息（包含发送者微信ID）
    CompressContent BLOB   -- 压缩内容（引用消息的XML数据）
);
```

#### Contact表（MicroMsg.db）
```sql
CREATE TABLE Contact (
    UserName TEXT,         -- 微信ID或群ID
    NickName TEXT,         -- 昵称
    Remark TEXT           -- 备注名
);
```

## 核心技术实现

### 1. 发送者昵称获取

**技术难点**：群聊消息的发送者信息不直接存储，需要从BytesExtra字段解析

```python
def extract_sender_wxid(bytes_extra):
    """从BytesExtra中提取发送者微信ID"""
    if not bytes_extra:
        return None
    
    try:
        bytes_str = str(bytes_extra)
        
        # 尝试base64解码
        try:
            decoded = base64.b64decode(bytes_str).decode('utf-8', errors='ignore')
            wxid_pattern = r'wxid_[a-zA-Z0-9]+'
            match = re.search(wxid_pattern, decoded)
            if match:
                return match.group(0)
        except:
            pass
        
        # 直接查找
        wxid_pattern = r'wxid_[a-zA-Z0-9]+'
        match = re.search(wxid_pattern, bytes_str)
        if match:
            return match.group(0)
        
        return None
    except:
        return None
```

### 2. 引用消息识别

**识别条件**：
- 消息类型：`Type = 49`
- 子类型：`SubType` 为 51 或 57
- 包含压缩内容：`CompressContent` 不为空

```python
def is_quote_message(msg):
    """判断是否为引用消息"""
    return (
        str(msg.get("type")) == "49" and 
        str(msg.get("subType")) in ["51", "57"] and 
        msg.get("compressContent") is not None
    )

def parse_quote_info(compress_content):
    """解析引用消息信息 - 只提取MsgSvrID"""
    if not compress_content:
        return None
    
    try:
        # base64解码
        decoded = base64.b64decode(compress_content).decode('utf-8', errors='ignore')
        
        # 只提取被引用消息的服务器ID (MsgSvrID)
        svrid_matches = re.findall(r'\b(\d{15,20})\b', decoded)
        if svrid_matches:
            return {
                "被引用消息ID": svrid_matches[0]
            }
        
        return None
        
    except Exception as e:
        return None
```

### 3. 统一ID体系

**关键决策**：全部使用MsgSvrID作为消息标识符

**原因**：
- 全局唯一性：跨设备一致
- 引用关系：被引用消息ID也是MsgSvrID
- 数据完整性：避免本地ID和服务器ID混用

```python
# 统一使用MsgSvrID作为消息ID
"消息ID": str(msg_svr_id) if msg_svr_id and str(msg_svr_id) != "0" else f"local_{local_id}"
```

### 4. 实时监控实现

```python
# 核心SQL查询
sql_new = f"""
SELECT localId, TalkerId, CreateTime, StrTalker, StrContent, IsSender, Type, SubType, BytesExtra, CompressContent, MsgSvrID
FROM MSG 
WHERE localId > {last_msg_id}
AND StrTalker LIKE '%@chatroom'
ORDER BY localId ASC;
"""
```

**监控策略**：
- 使用localId递增特性进行增量查询
- 每3秒轮询一次新消息
- 只监控群聊消息（StrTalker以@chatroom结尾）

## 输出格式规范

### 普通消息
```json
{
  "发送人": "小张",
  "发送时间": "2025-08-19 14:34:13",
  "群聊名称": "测试群",
  "消息内容": "测试消息内容",
  "消息类型": "1",
  "消息ID": "3264915067667192932",
  "引用信息": {}
}
```

### 引用消息
```json
{
  "发送人": "Wonder",
  "发送时间": "2025-08-19 14:25:35",
  "群聊名称": "测试群",
  "消息内容": "",
  "消息类型": "49",
  "消息ID": "2052665057659932943",
  "引用信息": {
    "被引用消息ID": "2640209026829637417"
  }
}
```

## 完整实现代码

### 主程序结构
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信群消息监控程序 - 正式版
"""

import requests
import json
import time
import datetime
import re
import base64

def extract_sender_wxid(bytes_extra):
    # 发送者微信ID提取逻辑
    pass

def is_quote_message(msg):
    # 引用消息判断逻辑
    pass

def parse_quote_info(compress_content):
    # 引用信息解析逻辑
    pass

def main():
    # 1. 连接微信RPC服务
    # 2. 获取数据库句柄
    # 3. 加载联系人信息
    # 4. 初始化监控起点
    # 5. 开始实时监控循环
    pass
```

## 关键技术要点

### 1. BytesExtra解析
- **数据格式**：base64编码的protobuf数据
- **提取目标**：发送者微信ID（格式：wxid_xxxxxxx）
- **解析方法**：base64解码 + 正则表达式匹配

### 2. CompressContent解析
- **数据格式**：base64编码的XML数据
- **提取目标**：被引用消息的MsgSvrID
- **解析方法**：base64解码 + 正则表达式提取长数字ID

### 3. 联系人昵称映射
- **优先级**：备注名 > 昵称 > 微信ID
- **缓存策略**：预加载常用联系人，避免频繁查询
- **性能优化**：限制查询数量，使用基础缓存兜底

### 4. ID体系统一
- **消息ID**：统一使用MsgSvrID
- **被引用消息ID**：同样使用MsgSvrID
- **兜底策略**：MsgSvrID为空时使用local_前缀的localId

## 部署和使用

### 1. 环境要求
- Python 3.6+
- requests库
- 微信客户端运行中
- 微信RPC服务运行在127.0.0.1:50007

### 2. 启动命令
```bash
python wechat_monitor_final.py
```

### 3. 预期输出
程序启动后会显示：
- 数据库连接状态
- 联系人加载数量
- 监控初始化信息
- 实时消息JSON输出

## 故障排除

### 常见问题
1. **连接失败**：检查微信RPC服务是否运行
2. **无消息输出**：确认群聊活跃度，发送测试消息
3. **昵称显示为ID**：检查联系人缓存加载情况
4. **引用解析失败**：验证CompressContent字段内容

### 调试方法
```python
# 测试连接
python test_connection.py

# 验证功能
python test_final_function.py
```

## 扩展功能

### 可能的扩展方向
1. **消息过滤**：按关键词、发送者过滤
2. **数据存储**：将监控结果存储到数据库
3. **实时推送**：集成webhook或消息队列
4. **多群管理**：选择性监控特定群聊
5. **消息统计**：生成群聊活跃度报告

## 技术总结

### 核心突破
1. **发送者识别**：成功解析BytesExtra获取真实发送者
2. **引用消息解析**：准确识别和提取引用关系
3. **ID体系统一**：解决了本地ID和服务器ID混用问题
4. **性能优化**：实现了稳定的实时监控

### 技术价值
- 完整的微信群聊数据获取方案
- 可扩展的消息处理架构
- 标准化的数据输出格式
- 跨设备一致的ID体系

**这是一个完整、稳定、可投入生产使用的微信群消息监控系统！**

---

## 给下一个AI的快速实现指南

### 🎯 核心需求
监控微信群消息，输出：发送人昵称、发送时间、群聊名称、消息内容、引用信息（JSON格式）

### 🔑 关键技术点

#### 1. 最重要：发送者昵称提取
```python
# 从BytesExtra字段解析发送者微信ID
def extract_sender_wxid(bytes_extra):
    decoded = base64.b64decode(str(bytes_extra)).decode('utf-8', errors='ignore')
    match = re.search(r'wxid_[a-zA-Z0-9]+', decoded)
    return match.group(0) if match else None
```

#### 2. 引用消息识别
```python
# 判断条件：Type=49 且 SubType=51或57
def is_quote_message(msg):
    return (str(msg.get("type")) == "49" and
            str(msg.get("subType")) in ["51", "57"] and
            msg.get("compressContent") is not None)

# 提取被引用消息ID
def parse_quote_info(compress_content):
    decoded = base64.b64decode(compress_content).decode('utf-8', errors='ignore')
    matches = re.findall(r'\b(\d{15,20})\b', decoded)
    return {"被引用消息ID": matches[0]} if matches else None
```

#### 3. 统一ID体系（重要！）
```python
# 全部使用MsgSvrID作为消息ID，保证唯一性
"消息ID": str(msg_svr_id) if msg_svr_id and str(msg_svr_id) != "0" else f"local_{local_id}"
```

### 📊 标准输出格式
```json
{
  "发送人": "小张",
  "发送时间": "2025-08-19 14:34:13",
  "群聊名称": "测试群",
  "消息内容": "消息文本",
  "消息类型": "1",
  "消息ID": "3264915067667192932",
  "引用信息": {"被引用消息ID": "2640209026829637417"}
}
```

### 🚀 核心SQL查询
```sql
SELECT localId, TalkerId, CreateTime, StrTalker, StrContent, IsSender, Type, SubType, BytesExtra, CompressContent, MsgSvrID
FROM MSG
WHERE localId > {last_msg_id}
AND StrTalker LIKE '%@chatroom'
ORDER BY localId ASC;
```

### 💡 实现提示
1. **连接信息**：RPC服务 `http://127.0.0.1:50007`，PID `22504`
2. **数据库**：MSG0.db（消息）+ MicroMsg.db（联系人）
3. **监控策略**：localId递增查询，3秒轮询
4. **性能优化**：限制联系人查询，使用基础缓存

### ⚠️ 关键注意事项
- BytesExtra和CompressContent都需要base64解码
- 引用消息的消息内容通常为空（Type=49）
- 必须统一使用MsgSvrID作为消息标识符
- 群聊过滤条件：StrTalker LIKE '%@chatroom'

### 📁 完整代码文件
参考 `wechat_monitor_final.py` - 这是经过完整测试的正式版本

**按照这个指南，30分钟内即可实现完整功能！**
