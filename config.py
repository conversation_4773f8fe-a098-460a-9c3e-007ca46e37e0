#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信群消息监控系统配置文件
"""

# RPC服务配置
RPC_HOST = "http://127.0.0.1:50007"
WECHAT_PID = "22504"  # 微信进程ID，根据实际情况修改

# 监控配置
MONITOR_INTERVAL = 3  # 监控间隔（秒）
MAX_CONTACTS_CACHE = 5000  # 最大联系人缓存数量

# 输出配置
OUTPUT_FORMAT = "json"  # 输出格式：json
SHOW_SELF_MESSAGES = False  # 是否显示自己发送的消息
SHOW_SYSTEM_MESSAGES = False  # 是否显示系统消息

# 过滤配置
FILTER_GROUPS = []  # 指定监控的群聊ID列表，空列表表示监控所有群聊
EXCLUDE_GROUPS = []  # 排除的群聊ID列表

# 关键词过滤
KEYWORD_FILTER = []  # 关键词过滤列表，只显示包含这些关键词的消息
EXCLUDE_KEYWORDS = []  # 排除关键词列表，不显示包含这些关键词的消息

# 调试配置
DEBUG_MODE = False  # 调试模式
LOG_SQL_QUERIES = False  # 是否记录SQL查询

# 数据库配置
DB_QUERY_TIMEOUT = 30  # 数据库查询超时时间（秒）
DB_RETRY_COUNT = 3  # 数据库查询重试次数

# 网络配置
REQUEST_TIMEOUT = 10  # HTTP请求超时时间（秒）
REQUEST_RETRY_COUNT = 3  # HTTP请求重试次数
