#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信群消息监控程序 - 增强版
包含错误处理、重试机制、过滤功能等
"""

import requests
import json
import time
import datetime
import re
import base64
import sys
from config import *
from logger import logger

class WeChatMonitorEnhanced:
    def __init__(self):
        self.rpc_host = RPC_HOST
        self.wechat_pid = WECHAT_PID
        self.headers = {
            "X-WeChat-PID": self.wechat_pid,
            "Content-Type": "application/json"
        }
        self.db_handles = {}
        self.contacts_cache = {}
        self.last_msg_id = 0
        self.debug_mode = DEBUG_MODE
        self.session = requests.Session()
        
    def extract_sender_wxid(self, bytes_extra):
        """从BytesExtra中提取发送者微信ID"""
        if not bytes_extra:
            return None
        
        try:
            bytes_str = str(bytes_extra)
            
            # 尝试base64解码
            try:
                decoded = base64.b64decode(bytes_str).decode('utf-8', errors='ignore')
                wxid_pattern = r'wxid_[a-zA-Z0-9]+'
                match = re.search(wxid_pattern, decoded)
                if match:
                    return match.group(0)
            except:
                pass
            
            # 直接查找
            wxid_pattern = r'wxid_[a-zA-Z0-9]+'
            match = re.search(wxid_pattern, bytes_str)
            if match:
                return match.group(0)
            
            return None
        except Exception as e:
            logger.debug(f"提取发送者微信ID异常: {e}")
            return None

    def is_quote_message(self, msg):
        """判断是否为引用消息"""
        return (
            str(msg.get("type")) == "49" and 
            str(msg.get("subType")) in ["51", "57"] and 
            msg.get("compressContent") is not None
        )

    def parse_quote_info(self, compress_content):
        """解析引用消息信息 - 只提取MsgSvrID"""
        if not compress_content:
            return None
        
        try:
            # base64解码
            decoded = base64.b64decode(compress_content).decode('utf-8', errors='ignore')
            
            # 只提取被引用消息的服务器ID (MsgSvrID)
            svrid_matches = re.findall(r'\b(\d{15,20})\b', decoded)
            if svrid_matches:
                return {
                    "被引用消息ID": svrid_matches[0]
                }
            
            return None
            
        except Exception as e:
            logger.debug(f"解析引用消息异常: {e}")
            return None

    def make_request(self, method, url, **kwargs):
        """带重试机制的HTTP请求"""
        for attempt in range(REQUEST_RETRY_COUNT):
            try:
                kwargs.setdefault('timeout', REQUEST_TIMEOUT)
                response = self.session.request(method, url, **kwargs)
                return response
            except Exception as e:
                logger.warning(f"HTTP请求失败 (尝试 {attempt + 1}/{REQUEST_RETRY_COUNT}): {e}")
                if attempt < REQUEST_RETRY_COUNT - 1:
                    time.sleep(1)
                else:
                    raise

    def get_db_info(self):
        """获取数据库信息"""
        try:
            response = self.make_request(
                "POST",
                f"{self.rpc_host}/api/db/getDBInfo",
                headers=self.headers,
                json={}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("Code") == 1:
                    db_info = data.get("Data", [])
                    for db in db_info:
                        db_name = db.get("databaseName", "")
                        if "MSG0.db" in db_name:
                            self.db_handles["MSG"] = db.get("handle")
                        elif "MicroMsg.db" in db_name:
                            self.db_handles["MicroMsg"] = db.get("handle")
                    
                    logger.info(f"数据库连接成功: MSG={self.db_handles.get('MSG')}, MicroMsg={self.db_handles.get('MicroMsg')}")
                    return True
                else:
                    logger.error(f"获取数据库信息失败: {data.get('Msg', 'Unknown error')}")
                    return False
            else:
                logger.error(f"请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"获取数据库信息异常: {e}")
            return False

    def execute_sql(self, db_handle, sql):
        """执行SQL查询"""
        for attempt in range(DB_RETRY_COUNT):
            try:
                if LOG_SQL_QUERIES:
                    logger.debug(f"执行SQL: {sql}")
                
                response = self.make_request(
                    "POST",
                    f"{self.rpc_host}/api/db/execSql",
                    headers=self.headers,
                    json={
                        "dbHandle": str(db_handle),
                        "sql": sql
                    }
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("Code") == 1:
                        return data.get("Data", [])
                    else:
                        logger.warning(f"SQL执行失败: {data.get('Msg', 'Unknown error')}")
                        return []
                else:
                    logger.warning(f"SQL请求失败: {response.status_code}")
                    return []
                    
            except Exception as e:
                logger.warning(f"SQL执行异常 (尝试 {attempt + 1}/{DB_RETRY_COUNT}): {e}")
                if attempt < DB_RETRY_COUNT - 1:
                    time.sleep(1)
                else:
                    logger.error(f"SQL执行最终失败: {sql}")
                    return []

    def load_contacts(self):
        """加载联系人信息到缓存"""
        if not self.db_handles.get("MicroMsg"):
            logger.error("MicroMsg数据库句柄未找到")
            return
        
        sql = f"SELECT UserName, NickName, Remark FROM Contact LIMIT {MAX_CONTACTS_CACHE};"
        contacts = self.execute_sql(self.db_handles["MicroMsg"], sql)
        
        for contact in contacts:
            username = contact.get("UserName", "")
            nickname = contact.get("NickName", "")
            remark = contact.get("Remark", "")
            
            # 优先级: 备注名 > 昵称 > 微信ID
            display_name = remark if remark else (nickname if nickname else username)
            self.contacts_cache[username] = display_name
        
        logger.info(f"联系人缓存加载完成，共 {len(self.contacts_cache)} 个联系人")

    def get_contact_name(self, wxid):
        """获取联系人显示名称"""
        if not wxid:
            return "未知用户"
            
        if wxid in self.contacts_cache:
            return self.contacts_cache[wxid]
        
        # 如果缓存中没有，尝试从数据库查询
        if self.db_handles.get("MicroMsg"):
            sql = f"SELECT NickName, Remark FROM Contact WHERE UserName = '{wxid}' LIMIT 1;"
            result = self.execute_sql(self.db_handles["MicroMsg"], sql)
            if result:
                contact = result[0]
                nickname = contact.get("NickName", "")
                remark = contact.get("Remark", "")
                display_name = remark if remark else (nickname if nickname else wxid)
                self.contacts_cache[wxid] = display_name
                return display_name
        
        return wxid

    def should_monitor_group(self, group_id):
        """判断是否应该监控该群聊"""
        # 如果指定了监控群聊列表
        if FILTER_GROUPS:
            return group_id in FILTER_GROUPS
        
        # 如果指定了排除群聊列表
        if EXCLUDE_GROUPS:
            return group_id not in EXCLUDE_GROUPS
        
        return True

    def should_show_message(self, content):
        """判断是否应该显示该消息"""
        # 关键词过滤
        if KEYWORD_FILTER:
            if not any(keyword in content for keyword in KEYWORD_FILTER):
                return False
        
        # 排除关键词
        if EXCLUDE_KEYWORDS:
            if any(keyword in content for keyword in EXCLUDE_KEYWORDS):
                return False
        
        return True

    def initialize_monitor(self):
        """初始化监控起点"""
        if not self.db_handles.get("MSG"):
            logger.error("MSG数据库句柄未找到")
            return False
        
        # 获取最新的localId作为监控起点
        sql = "SELECT MAX(localId) as max_id FROM MSG WHERE StrTalker LIKE '%@chatroom';"
        result = self.execute_sql(self.db_handles["MSG"], sql)
        
        if result and result[0].get("max_id"):
            self.last_msg_id = result[0]["max_id"]
            logger.info(f"监控初始化完成，起始消息ID: {self.last_msg_id}")
            return True
        else:
            logger.warning("监控初始化失败，无法获取起始消息ID，将从0开始")
            self.last_msg_id = 0
            return True

    def monitor_messages(self):
        """实时监控新消息"""
        logger.info("开始监控微信群消息...")
        logger.info("=" * 50)
        
        consecutive_errors = 0
        max_consecutive_errors = 10
        
        while True:
            try:
                # 查询新消息
                sql = f"""
                SELECT localId, TalkerId, CreateTime, StrTalker, StrContent, IsSender, Type, SubType, BytesExtra, CompressContent, MsgSvrID
                FROM MSG 
                WHERE localId > {self.last_msg_id}
                AND StrTalker LIKE '%@chatroom'
                ORDER BY localId ASC;
                """
                
                new_messages = self.execute_sql(self.db_handles["MSG"], sql)
                
                if new_messages:
                    consecutive_errors = 0  # 重置错误计数
                    
                for msg in new_messages:
                    if self.process_message(msg):
                        self.last_msg_id = msg.get("localId", self.last_msg_id)
                
                # 根据配置的间隔检查
                time.sleep(MONITOR_INTERVAL)
                
            except KeyboardInterrupt:
                logger.info("监控已停止")
                break
            except Exception as e:
                consecutive_errors += 1
                logger.error(f"监控异常 ({consecutive_errors}/{max_consecutive_errors}): {e}")
                
                if consecutive_errors >= max_consecutive_errors:
                    logger.error("连续错误次数过多，程序退出")
                    break
                
                time.sleep(5)

    def process_message(self, msg):
        """处理单条消息"""
        try:
            # 基本信息
            local_id = msg.get("localId")
            msg_svr_id = msg.get("MsgSvrID")
            create_time = msg.get("CreateTime")
            str_talker = msg.get("StrTalker", "")
            str_content = msg.get("StrContent", "")
            is_sender = msg.get("IsSender", 0)
            msg_type = msg.get("Type")
            sub_type = msg.get("SubType")
            bytes_extra = msg.get("BytesExtra")
            compress_content = msg.get("CompressContent")
            
            # 群聊过滤
            if not self.should_monitor_group(str_talker):
                return True
            
            # 跳过自己发送的消息（除非配置允许显示）
            if is_sender == 1 and not SHOW_SELF_MESSAGES:
                return True
            
            # 消息内容过滤
            if not self.should_show_message(str_content):
                return True
            
            # 格式化时间
            if create_time:
                formatted_time = datetime.datetime.fromtimestamp(create_time).strftime("%Y-%m-%d %H:%M:%S")
            else:
                formatted_time = "未知时间"
            
            # 获取群聊名称
            group_name = self.get_contact_name(str_talker)
            
            # 获取发送者昵称
            sender_wxid = self.extract_sender_wxid(bytes_extra)
            sender_name = self.get_contact_name(sender_wxid) if sender_wxid else "未知发送者"
            
            # 统一使用MsgSvrID作为消息ID
            message_id = str(msg_svr_id) if msg_svr_id and str(msg_svr_id) != "0" else f"local_{local_id}"
            
            # 处理引用信息
            quote_info = {}
            if self.is_quote_message(msg):
                quote_data = self.parse_quote_info(compress_content)
                if quote_data:
                    quote_info = quote_data
            
            # 构建输出消息
            output_msg = {
                "发送人": sender_name,
                "发送时间": formatted_time,
                "群聊名称": group_name,
                "消息内容": str_content,
                "消息类型": str(msg_type),
                "消息ID": message_id,
                "引用信息": quote_info
            }
            
            # 输出JSON格式消息
            print(json.dumps(output_msg, ensure_ascii=False, indent=2))
            print("-" * 30)
            
            return True
            
        except Exception as e:
            logger.error(f"处理消息异常: {e}")
            return False

    def run(self):
        """运行监控程序"""
        logger.info("微信群消息监控系统启动中...")
        
        # 1. 获取数据库信息
        if not self.get_db_info():
            logger.error("无法获取数据库信息，程序退出")
            return
        
        # 2. 加载联系人信息
        self.load_contacts()
        
        # 3. 初始化监控起点
        if not self.initialize_monitor():
            logger.error("监控初始化失败，程序退出")
            return
        
        # 4. 开始实时监控
        self.monitor_messages()

def main():
    """主函数"""
    monitor = WeChatMonitorEnhanced()
    monitor.run()

if __name__ == "__main__":
    main()
