# 微信群消息监控系统 - 项目文件说明

## 文件结构

```
微信群消息监控系统/
├── wechat_monitor.py              # 基础版监控程序
├── wechat_monitor_enhanced.py     # 增强版监控程序（推荐使用）
├── test_connection.py             # RPC连接测试脚本
├── config.py                      # 配置文件
├── logger.py                      # 日志记录模块
├── start_monitor.bat              # Windows启动脚本
├── README.md                      # 使用说明文档
├── 微信群消息监控系统-完整技术文档.md  # 技术文档
├── 微信接口文档.txt                # 微信RPC接口文档
└── 项目说明.md                    # 本文件
```

## 文件详细说明

### 核心程序文件

#### 1. `wechat_monitor.py` - 基础版监控程序
- **功能**: 实现基本的微信群消息监控功能
- **特点**: 代码简洁，功能完整
- **适用**: 简单使用场景，不需要复杂配置

#### 2. `wechat_monitor_enhanced.py` - 增强版监控程序（推荐）
- **功能**: 完整的监控功能 + 增强特性
- **增强特性**:
  - 错误处理和重试机制
  - 群聊过滤功能
  - 关键词过滤
  - 日志记录
  - 配置文件支持
  - 连接稳定性优化
- **适用**: 生产环境，需要稳定运行

### 配置和工具文件

#### 3. `config.py` - 配置文件
- **功能**: 集中管理所有配置参数
- **主要配置项**:
  - RPC服务地址和端口
  - 微信进程ID
  - 监控间隔时间
  - 过滤规则
  - 调试选项

#### 4. `logger.py` - 日志记录模块
- **功能**: 提供统一的日志记录功能
- **特性**:
  - 控制台输出
  - 文件日志（调试模式）
  - 多级别日志（INFO、WARNING、ERROR、DEBUG）

#### 5. `test_connection.py` - 连接测试脚本
- **功能**: 测试微信RPC服务连接状态
- **测试项目**:
  - 微信客户端列表
  - 数据库连接
  - 用户信息获取

#### 6. `start_monitor.bat` - Windows启动脚本
- **功能**: 一键启动监控程序
- **特性**:
  - 自动检查Python环境
  - 自动安装依赖
  - 连接测试
  - 启动监控

### 文档文件

#### 7. `README.md` - 使用说明文档
- **内容**: 详细的使用指南和配置说明
- **包含**: 安装、配置、使用、故障排除

#### 8. `微信群消息监控系统-完整技术文档.md` - 技术文档
- **内容**: 完整的技术实现细节
- **包含**: 架构设计、核心算法、数据库结构

#### 9. `微信接口文档.txt` - 微信RPC接口文档
- **内容**: 微信RPC服务的完整API文档
- **包含**: 所有可用接口的详细说明

## 使用建议

### 新手用户
1. 先运行 `test_connection.py` 测试连接
2. 使用 `start_monitor.bat` 一键启动
3. 或直接运行 `python wechat_monitor.py`

### 高级用户
1. 修改 `config.py` 进行个性化配置
2. 运行 `python wechat_monitor_enhanced.py`
3. 根据需要启用调试模式和日志记录

### 开发者
1. 参考技术文档了解实现原理
2. 基于现有代码进行功能扩展
3. 使用日志模块进行调试

## 配置要点

### 必须配置项
- `WECHAT_PID`: 微信进程ID（根据实际情况修改）
- `RPC_HOST`: RPC服务地址（通常为 http://127.0.0.1:50007）

### 可选配置项
- `MONITOR_INTERVAL`: 监控间隔（默认3秒）
- `FILTER_GROUPS`: 指定监控的群聊
- `KEYWORD_FILTER`: 关键词过滤
- `DEBUG_MODE`: 调试模式

## 运行环境

### 系统要求
- Windows 10/11
- Python 3.6+
- 微信客户端

### 依赖库
- requests（HTTP请求）

### 服务要求
- 微信RPC服务运行在 127.0.0.1:50007
- 微信客户端保持登录状态

## 故障排除

### 常见问题
1. **连接失败**: 检查RPC服务状态
2. **无消息输出**: 确认群聊活跃度
3. **昵称显示异常**: 检查联系人缓存
4. **程序崩溃**: 查看错误日志

### 调试方法
1. 启用 `DEBUG_MODE = True`
2. 查看 `monitor.log` 日志文件
3. 运行连接测试脚本
4. 检查配置文件设置

## 扩展开发

### 可扩展功能
1. 数据库存储
2. Web界面
3. 消息推送
4. 统计分析
5. 自动回复

### 开发建议
1. 基于增强版进行扩展
2. 遵循现有的代码结构
3. 使用配置文件管理参数
4. 添加适当的错误处理

---

**选择适合你需求的版本开始使用吧！**
