@echo off
chcp 65001 >nul
echo 微信群消息监控系统
echo ==================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境，请先安装Python 3.6+
    pause
    exit /b 1
)

echo 正在检查依赖库...
python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo 正在安装requests库...
    pip install requests
    if errorlevel 1 (
        echo 错误: 无法安装requests库
        pause
        exit /b 1
    )
)

echo.
echo 正在测试微信RPC连接...
python test_connection.py
if errorlevel 1 (
    echo.
    echo 警告: RPC连接测试失败，但仍将启动监控程序
    echo 请确保微信客户端正在运行且RPC服务已启动
    echo.
    pause
)

echo.
echo 启动消息监控程序...
echo 按 Ctrl+C 停止监控
echo.
python wechat_monitor.py

pause
