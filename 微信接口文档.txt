
接口解释：
整体结构
这个 JSON 文件主要包含以下几个顶级键：

info: 包含这个 Postman 收藏集的基本信息，如名称、ID、描述 schema 的链接等。
name: "WeChat'RPC" - 表明这个集合是用于微信相关的远程过程调用。
item: 这是核心部分，包含了所有API请求的定义，这些请求被组织在不同的文件夹 (folder) 中。
event: 定义了在特定事件（如预请求 prerequest 或测试 test）发生时执行的脚本。这里的全局事件脚本是空的。
variable: 定义了收藏集级别的变量。
HOST: http://127.0.0.1:50007 - 这是 API 服务的基础 URL，看起来是部署在本地的。
WECHAT_PID: 初始值为空，这个变量通常会在某个请求成功后被动态设置（例如，在“微信客户端列表”请求的测试脚本中）。
API 请求详解 (item 数组)
item 数组中的每个对象代表一个文件夹或一个直接的请求。这里，它主要由多个文件夹构成，每个文件夹下有具体的 API 请求。

1. 文件夹: "WeChat" (微信客户端管理)
微信客户端列表 (GET {{HOST}}/api/wechat/list):
获取当前运行的微信客户端实例列表。
重要: 这个请求的 event (测试脚本) 中有一行代码: pm.collectionVariables.set("WECHAT_PID", pm.response.json().Data[0].Pid);。这意味着当这个请求成功后，它会从响应的 JSON 数据中提取第一个客户端的 Pid (进程ID)，并将其设置为集合变量 WECHAT_PID。后续很多请求都需要这个 WECHAT_PID。
启动新的微信客户端 (GET {{HOST}}/api/wechat/login):
用于启动一个新的微信客户端实例并进行登录。
微信支持版本列表 (GET {{HOST}}/api/wechat/support-versions):
获取该 RPC 服务支持的微信客户端版本列表。
退出微信客户端 (POST {{HOST}}/api/wechat/logout):
需要请求头 X-WeChat-PID: {{WECHAT_PID}}。
用于退出指定的微信客户端。
恢复微信原始状态 (POST {{HOST}}/api/wechat/unload-helper):
需要请求头 X-WeChat-PID: {{WECHAT_PID}}。
可能是用于卸载 RPC 助手，使微信恢复到没有被外部程序控制的状态。
2. 文件夹: "ChatRoom" (群聊管理)
所有请求基本都是 POST 方法，并在请求头中携带 X-WeChat-PID: {{WECHAT_PID}}，请求体为 JSON 格式。

创建群 (/api/chatroom/create): body: {"memberIds": ["wxid_123"]} - 用指定的成员创建群聊。
获取群信息 (/api/chatroom/detail): body: {"chatRoomId":"14460947661@chatroom"} - 获取指定群聊的详细信息。
添加群成员 (/api/chatroom/addMember): body: {"chatRoomId": "123@chatroom", "memberIds": ["wxid_123"]} - 向指定群聊添加成员。
修改群昵称 (/api/chatroom/modifyNickname): body: {"chatRoomId": "123@chatroom", "wxid": "wxid_123", "nickName": "nickName"} - 修改指定群成员在群内的昵称。
删除群成员 (/api/chatroom/deleteMember): body: {"chatRoomId": "123@chatroom", "memberIds": ["wxid_123"]} - 从群聊中删除成员。
获取群成员 (/api/chatroom/getMembers): body: {"chatRoomId":"14460947661@chatroom"} - 获取群聊的成员列表。
邀请进群（>40人） (/api/chatroom/inviteMember): body: {"chatRoomId": "123@chatroom", "memberIds": ["wxid_123"]} - 邀请用户加入群聊（可能特指超过一定人数限制后的邀请方式）。
退出群聊 (/api/chatroom/quit): body: {"chatRoomId":"123@chatroom"} - 当前登录用户退出指定群聊。
修改群名 (/api/chatroom/changeName): body: {"chatRoomId": "123@chatroom", "roomName": "roomName"} - 修改群聊的名称。
修改群公告 (/api/chatroom/changeNotice): body: {"chatRoomId": "123@chatroom", "notice": "notice"} - 修改群聊的公告。
3. 文件夹: "DB" (数据库操作)
获取数据库信息 (/api/db/getDBInfo): 获取微信相关的数据库信息（如句柄、路径等）。
执行sql (/api/db/execSql): body: {"dbHandle": "1188434278240", "sql": "select * from Contact;"} - 在指定的数据库句柄上执行 SQL 查询。
4. 文件夹: "Msg" (消息管理)
发送文本 (/api/msg/sendTextMsg): body: {"wxid": "wxid_123", "msg": "text"} - 向指定用户或群聊发送文本消息。
发送at (/api/msg/sendAtText): body: {"wxids": ["wxid_123"], "chatRoomId": "123@chatroom", "msg": "text"} - 在群聊中发送 @某人 的文本消息。
发送文件 (/api/msg/sendFileMsg): body: {"wxid": "wxid_123", "filePath": "C:\\1.txt"} - 发送文件。
转发公众号消息 (/api/msg/forwardPublicMsg): body 包含公众号文章的详细信息（appName, userName, title, url, thumbUrl, digest, wxid） - 转发公众号文章。
转发公众号消息（通过消息id） (/api/msg/forwardPublicMsgByMsgId): body: {"msgId":"123"} - 通过消息 ID 转发公众号文章。
发送图片 (/api/msg/sendImagesMsg): body: {"wxid": "wxid_123", "imagePath": "C:\\1.png"} - 发送图片。
发送自定义表情 (/api/msg/sendCustomEmotion): body: {"wxid": "wxid_123", "filePath": "C:\\1.gif"} - 发送自定义表情（如GIF）。
发送小程序 (/api/msg/sendApplet): body 包含小程序的详细参数 - 发送小程序卡片。
发送拍一拍 (/api/msg/sendPatMsg): body: {"wxid": "wxid_123", "receiver": "wxid_123"} - 发送拍一拍提醒。
撤回消息 (/api/msg/revokeMsg): body: {"msgId":"123"} - 撤回已发送的消息。
发送名片 (/api/msg/sendCardMsg): body: {"wxid": "wxid_123", "targetId": "wxid_123"} - 发送指定用户的名片给另一个用户/群。
置顶消息 (/api/msg/topMsg): body: {"msgId":"123"} - 在群聊中置顶某条消息。
取消置顶 (/api/msg/removeTopMsg): body: {"msgId": "123", "chatRoomId": "123@chatroom"} - 取消群聊中消息的置顶。
转发消息 (/api/msg/forwardMsg): body: {"msgId": "123", "wxid": "wxid_123"} - 转发某条消息给指定用户/群。
发送链接 (/api/msg/sendLinkMsg): body 包含链接的 title, url, thumbUrl, digest, wxid - 发送链接卡片。
获取聊天记录 (/api/msg/getMsgList): body: {"wxid":"wxid_4643316432811", "startDate":"2024-08-24", "endDate":"2024-09-24", "dateType":2} - 获取指定用户/群在特定日期范围内的聊天记录。
获取聊天记录分页 (/api/msg/getMsgListByPage): body: {"limit":10, "isSortDesc":true, "wxid":"9948090198@chatroom"} - 分页获取聊天记录。
5. 文件夹: "Other" (其他功能)
添加到收藏 (/api/other/addFavFromMsg): body: {"msgId":"123"} - 将某条消息添加到微信收藏。
收藏图片 (/api/other/addFavFromImage): body: {"msgId":"123", "imagePath":"C:\\1.png"} - 将本地图片作为消息收藏（可能需要关联一个msgId）。
下载附件（暂时无效，等待修复） (/api/other/downloadAttach): body: {"msgId":"7755058781297840811", "storeDir":"C:\\Users\\<USER>\\Desktop"} - 下载消息中的附件。
下载图片和视频 (/api/other/downloadMedia): body 包含 fileId, aesKey, msgId, fileType, savePath - 下载微信中的媒体文件（图片/视频）。
解码图片 (/api/other/decodeImage): body: {"filePath":"C:\\Users\\<USER>\\Desktop\\response.dat", "storeDir":"C:\\Users\\<USER>\\Desktop\\"} - 解码微信加密的图片文件（通常是 .dat 文件）。
解码朋友圈媒体 (/api/other/decodeSnsMedia): body: {"filePath":"C:\\...", "storePath":"C:\\...", "key":"..."} - 解码朋友圈中的媒体文件。
获取语音 (/api/other/getVoiceByMsgId): body: {"msgId":"123", "storeDir":"C:\\voice"} - 下载消息中的语音文件。
ocr识别 (/api/other/ocr): body: {"imagePath":"C:\\1.jpg"} - 对指定图片进行 OCR 文字识别。
数据库解密 (/api/other/decodeSqlite): body: {"sourcePath": "...", "destPath": "...", "dbkey": "..."} - 解密微信的 SQLite 数据库文件。
获取主窗体句柄 (/api/other/getMainHwnd): 获取微信主窗口的句柄。
获取主窗体句柄V2 (/api/other/getMainHwndV2): 可能是获取主窗口句柄的另一个版本。
6. 文件夹: "Pay" (支付相关)
收款 (/api/pay/getPayTransfer): body: {"wxid":"wxid_123", "transferid":"..."} - 接收转账。
7. 文件夹: "Session" (会话管理)
置顶会话 (/api/session/topSession): body: {"sessionId":"wxid_123", "top":1} - 置顶或取消置顶会话 (1为置顶, 0为取消)。
免打扰 (/api/session/mute): body: {"sessionId":"wxid_123", "enable":1} - 设置或取消会话免打扰 (1为开启免打扰, 0为关闭)。
添加到通讯录 (/api/session/addToContact): body: {"sessionId":"wxid_123", "delete":0} - 将会话对象添加到通讯录 (0为添加, 1可能为移出)。
是否置顶会话 (/api/session/isTopSession): body: {"sessionId":"wxid_123"} - 检查会话是否已置顶。
是否免打扰 (/api/session/isMute): body: {"sessionId":"wxid_123"} - 检查会话是否已设置为免打扰。
设置为已读 (/api/session/setSessionReaded): body: {"wxid":"wxid_123"} - 将会话标记为已读。
获取会话列表 (/api/session/getSessionList): 获取当前微信的会话列表。
8. 文件夹: "Sns" (朋友圈相关)
朋友圈首页 (/api/sns/getSNSFirstPage): 获取朋友圈的第一页内容。
朋友圈下一页 (/api/sns/getSNSNextPage): body: {"snsId":"14473879396511593025"} - 根据上一页最后一条朋友圈的 snsId 获取下一页内容。
9. 文件夹: "User" (用户与联系人管理)
检查登录状态 (/api/user/checkLogin): 检查当前微信的登录状态。
当前账号信息 (/api/user/userInfo): 获取当前登录微信账号的个人信息。
通过添加好友 (/api/user/doVerifyUser): body: {"encryptusername":"", "ticket":"", "scene":1} - 通过好友验证请求。
更改备注 (/api/user/changeRemark): body: {"wxid":"wxid_123", "remark":"remark"} - 修改好友备注。
删除好友 (/api/user/deleteFriend): body: {"wxid":"wxid_123"} - 删除好友。
是否为好友 (/api/user/getContact): body: {"wxid":"wxid_1dme58g6h9mi22"} - 检查指定 wxid 是否为好友 (接口名 getContact 似乎不准确，可能实际是 isFriend)。
联系人信息 (/api/user/getContactProfile): body: {"wxid":"wxid_ccun37bujro22", "useCache":true} - 获取指定联系人的详细资料，可选择是否使用缓存。
所有标签 (/api/user/getContactLabelList): 获取当前账号的所有联系人标签。
联系人标签 (/api/user/getContactLabel): body: {"wxid":"wxid_123"} - 获取指定联系人的标签。
获取用户头像 (/api/user/getHeadImgUrlByUserName): body: {"wxid":"wxid_123"} - 获取指定用户的头像 URL。
联系人列表 (/api/user/getContactList): 获取当前账号的通讯录好友列表。
加群好友 (/api/user/addFriendFromGroup): body: {"wxid": "wxid_123", "chatRoomId": "123@chatroom", "text": "Hi~"} - 从群聊中添加好友并发送验证信息。
总结
这套 Postman 接口定义了一个功能非常全面的微信 RPC 服务，允许开发者通过 HTTP 请求来控制微信客户端执行各种操作，包括：

管理微信客户端本身（登录、退出、获取列表）。
精细化的群聊管理（创建、加人、踢人、改名、公告等）。
数据库操作（执行 SQL）。
多类型的消息收发与管理（文本、图片、文件、链接、小程序、名片、@、撤回、转发等）。
朋友圈浏览。
用户和联系人管理（获取信息、加好友、改备注、标签管理等）。
会话管理（置顶、免打扰、标记已读等）。
其他实用功能（OCR、文件下载/解码、收款、收藏等）。
使用时，通常需要先调用 "微信客户端列表" 接口来获取并设置 WECHAT_PID 变量，然后后续依赖此 PID 的接口才能正常工作。所有请求都指向本地的 http://127.0.0.1:50007 服务。






原代码：
{
"info": {
"_postman_id": "90cebe68-50d5-4fb5-a706-e2e173cacfd4",
"name": "WeChat'RPC",
"schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json",
"_exporter_id": "40788697",
"_collection_link": "https://red-resonance-195948.postman.co/workspace/WeChatHelper~43f0c617-6248-4d08-b175-3c0cd87878f6/collection/7801970-90cebe68-50d5-4fb5-a706-e2e173cacfd4?action=share&source=collection_link&creator=40788697"
},
"item": [
{
"name": "WeChat",
"item": [
{
"name": "微信客户端列表",
"event": [
{
"listen": "test",
"script": {
"exec": [
"pm.collectionVariables.set("WECHAT_PID", pm.response.json().Data[0].Pid);"
],
"type": "text/javascript",
"packages": {}
}
}
],
"request": {
"method": "GET",
"header": [],
"url": {
"raw": "{{HOST}}/api/wechat/list",
"host": [
"{{HOST}}"
],
"path": [
"api",
"wechat",
"list"
]
}
},
"response": []
},
{
"name": "启动新的微信客户端",
"request": {
"method": "GET",
"header": [],
"url": {
"raw": "{{HOST}}/api/wechat/login",
"host": [
"{{HOST}}"
],
"path": [
"api",
"wechat",
"login"
]
}
},
"response": []
},
{
"name": "微信支持版本列表",
"request": {
"method": "GET",
"header": [],
"url": {
"raw": "{{HOST}}/api/wechat/support-versions",
"host": [
"{{HOST}}"
],
"path": [
"api",
"wechat",
"support-versions"
]
}
},
"response": []
},
{
"name": "退出微信客户端",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
}
],
"url": {
"raw": "{{HOST}}/api/wechat/logout",
"host": [
"{{HOST}}"
],
"path": [
"api",
"wechat",
"logout"
]
}
},
"response": []
},
{
"name": "恢复微信原始状态",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
}
],
"url": {
"raw": "{{HOST}}/api/wechat/unload-helper",
"host": [
"{{HOST}}"
],
"path": [
"api",
"wechat",
"unload-helper"
]
}
},
"response": []
}
]
},
{
"name": "ChatRoom",
"item": [
{
"name": "创建群",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "memberIds": ["wxid_123"]\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/chatroom/create",
"host": [
"{{HOST}}"
],
"path": [
"api",
"chatroom",
"create"
]
}
},
"response": [
{
"name": "创建群",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "memberIds": ["wxid_123"]\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/chatroom/create",
"host": [
"{{HOST}}"
],
"path": [
"api",
"chatroom",
"create"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "获取群信息",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "chatRoomId":"14460947661@chatroom"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/chatroom/detail",
"host": [
"{{HOST}}"
],
"path": [
"api",
"chatroom",
"detail"
]
}
},
"response": [
{
"name": "获取群信息",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "chatRoomId":"123@chatroom"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/chatroom/detail",
"host": [
"{{HOST}}"
],
"path": [
"api",
"chatroom",
"detail"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "添加群成员",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "chatRoomId": "123@chatroom",\r\n  "memberIds": ["wxid_123"]\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/chatroom/addMember",
"host": [
"{{HOST}}"
],
"path": [
"api",
"chatroom",
"addMember"
]
}
},
"response": [
{
"name": "添加群成员",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "chatRoomId": "123@chatroom",\r\n  "memberIds": ["wxid_123"]\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/chatroom/addMember",
"host": [
"{{HOST}}"
],
"path": [
"api",
"chatroom",
"addMember"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "修改群昵称",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "chatRoomId": "123@chatroom",\r\n  "wxid": "wxid_123",\r\n  "nickName": "nickName"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/chatroom/modifyNickname",
"host": [
"{{HOST}}"
],
"path": [
"api",
"chatroom",
"modifyNickname"
]
}
},
"response": [
{
"name": "修改群昵称",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "chatRoomId": "123@chatroom",\r\n  "wxid": "wxid_123",\r\n  "nickName": "nickName"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/chatroom/modifyNickname",
"host": [
"{{HOST}}"
],
"path": [
"api",
"chatroom",
"modifyNickname"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "删除群成员",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "chatRoomId": "123@chatroom",\r\n  "memberIds": ["wxid_123"]\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/chatroom/deleteMember",
"host": [
"{{HOST}}"
],
"path": [
"api",
"chatroom",
"deleteMember"
]
}
},
"response": [
{
"name": "删除群成员",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "chatRoomId": "123@chatroom",\r\n  "memberIds": ["wxid_123"]\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/chatroom/deleteMember",
"host": [
"{{HOST}}"
],
"path": [
"api",
"chatroom",
"deleteMember"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "获取群成员",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "chatRoomId":"14460947661@chatroom"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/chatroom/getMembers",
"host": [
"{{HOST}}"
],
"path": [
"api",
"chatroom",
"getMembers"
]
}
},
"response": [
{
"name": "获取群成员",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "chatRoomId":"123@chatroom"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/chatroom/getMembers",
"host": [
"{{HOST}}"
],
"path": [
"api",
"chatroom",
"getMembers"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "邀请进群（>40人）",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "chatRoomId": "123@chatroom",\r\n  "memberIds": ["wxid_123"]\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/chatroom/inviteMember",
"host": [
"{{HOST}}"
],
"path": [
"api",
"chatroom",
"inviteMember"
]
}
},
"response": [
{
"name": "邀请进群（>40人）",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "chatRoomId": "123@chatroom",\r\n  "memberIds": ["wxid_123"]\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/chatroom/inviteMember",
"host": [
"{{HOST}}"
],
"path": [
"api",
"chatroom",
"inviteMember"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "退出群聊",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "chatRoomId":"123@chatroom"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/chatroom/quit",
"host": [
"{{HOST}}"
],
"path": [
"api",
"chatroom",
"quit"
]
}
},
"response": [
{
"name": "退出群聊",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "chatRoomId":"123@chatroom"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/chatroom/quit",
"host": [
"{{HOST}}"
],
"path": [
"api",
"chatroom",
"quit"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "修改群名",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "chatRoomId": "123@chatroom",\r\n  "roomName": "roomName"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/chatroom/changeName",
"host": [
"{{HOST}}"
],
"path": [
"api",
"chatroom",
"changeName"
]
}
},
"response": [
{
"name": "修改群名",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "chatRoomId": "123@chatroom",\r\n  "roomName": "roomName"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/chatroom/changeName",
"host": [
"{{HOST}}"
],
"path": [
"api",
"chatroom",
"changeName"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "修改群公告",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "chatRoomId": "123@chatroom",\r\n  "notice": "notice"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/chatroom/changeNotice",
"host": [
"{{HOST}}"
],
"path": [
"api",
"chatroom",
"changeNotice"
]
}
},
"response": [
{
"name": "修改群公告",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "chatRoomId": "123@chatroom",\r\n  "notice": "notice"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/chatroom/changeNotice",
"host": [
"{{HOST}}"
],
"path": [
"api",
"chatroom",
"changeNotice"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
}
]
},
{
"name": "DB",
"item": [
{
"name": "获取数据库信息",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/db/getDBInfo",
"host": [
"{{HOST}}"
],
"path": [
"api",
"db",
"getDBInfo"
]
}
},
"response": []
},
{
"name": "执行sql",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "dbHandle": "1188434278240",\r\n  "sql": "select * from Contact;"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/db/execSql",
"host": [
"{{HOST}}"
],
"path": [
"api",
"db",
"execSql"
]
}
},
"response": [
{
"name": "执行sql",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "dbHandle": "1188434278240",\r\n  "sql": "select * from Contact;"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/db/execSql",
"host": [
"{{HOST}}"
],
"path": [
"api",
"db",
"execSql"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
}
]
},
{
"name": "Msg",
"item": [
{
"name": "发送文本",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "wxid": "wxid_123",\r\n  "msg": "text"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/sendTextMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"sendTextMsg"
]
}
},
"response": [
{
"name": "发送文本",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "wxid": "wxid_123",\r\n  "msg": "text"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/sendTextMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"sendTextMsg"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "发送at",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "wxids": ["wxid_123"],\r\n  "chatRoomId": "123@chatroom",\r\n  "msg": "text"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/sendAtText",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"sendAtText"
]
}
},
"response": [
{
"name": "发送at",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "wxids": ["wxid_123"],\r\n  "chatRoomId": "123@chatroom",\r\n  "msg": "text"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/sendAtText",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"sendAtText"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "发送文件",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "wxid": "wxid_123",\r\n  "filePath": "C:\\1.txt"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/sendFileMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"sendFileMsg"
]
}
},
"response": [
{
"name": "发送文件",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "wxid": "wxid_123",\r\n  "filePath": "C:\\1.txt"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/sendFileMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"sendFileMsg"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "转发公众号消息",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "\r\n{\r\n    "appName": "快手",\r\n    "userName": "gh_271633",\r\n    "title": "PC硬件、数码产品彻底反转",\r\n    "url": "http://mp.weixin.qq.com/s?__biz=Mzg3MzYg==&mid=22440&idx=1&sn=bd8e8b0d9f2753f3c340&chksm=ced16f2ff9a6e639cc9bb76631ff03487f86486f0f29fcf9f8bed754354cb20eda31cc894a56&scene=0&xtrack=1#rd",\r\n    "thumbUrl": "https://mmbiz.qpic.cn/sz__jpg/tpzwaqMCicQyEkpxmpmmP9KgoBHiciamYhqZ0ff4kNlozxgRq4AtEzibo4iaw/640?wxtype=jpeg&wxfrom=0",\r\n    "digest": "这谁顶得住？",\r\n    "wxid": "filehelper"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/forwardPublicMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"forwardPublicMsg"
]
}
},
"response": [
{
"name": "转发公众号消息",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "\r\n{\r\n    "appName": "快手",\r\n    "userName": "gh_271633",\r\n    "title": "PC硬件、数码产品彻底反转",\r\n    "url": "http://mp.weixin.qq.com/s?__biz=Mzg3MzYg==&mid=22440&idx=1&sn=bd8e8b0d9f2753f3c340&chksm=ced16f2ff9a6e639cc9bb76631ff03487f86486f0f29fcf9f8bed754354cb20eda31cc894a56&scene=0&xtrack=1#rd",\r\n    "thumbUrl": "https://mmbiz.qpic.cn/sz__jpg/tpzwaqMCicQyEkpxmpmmP9KgoBHiciamYhqZ0ff4kNlozxgRq4AtEzibo4iaw/640?wxtype=jpeg&wxfrom=0",\r\n    "digest": "这谁顶得住？",\r\n    "wxid": "filehelper"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/forwardPublicMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"forwardPublicMsg"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "转发公众号消息（通过消息id）",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "msgId":"123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/forwardPublicMsgByMsgId",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"forwardPublicMsgByMsgId"
]
}
},
"response": [
{
"name": "转发公众号消息（通过消息id）",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "msgId":"123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/forwardPublicMsgByMsgId",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"forwardPublicMsgByMsgId"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "发送图片",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "wxid": "wxid_123",\r\n  "imagePath": "C:\\1.png"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/sendImagesMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"sendImagesMsg"
]
}
},
"response": [
{
"name": "发送图片",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "wxid": "wxid_123",\r\n  "imagePath": "C:\\1.png"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/sendImagesMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"sendImagesMsg"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "发送自定义表情",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "wxid": "wxid_123",\r\n  "filePath": "C:\\1.gif"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/sendCustomEmotion",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"sendCustomEmotion"
]
}
},
"response": [
{
"name": "发送自定义表情",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "wxid": "wxid_123",\r\n  "filePath": "C:\\1.gif"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/sendCustomEmotion",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"sendCustomEmotion"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "发送小程序",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n"wxid":"filehelper",\r\n"waidConcat":"wxaf35009675aa0b2a_118",\r\n"waid":"wxaf35009675aa0b2a",\r\n"appletWxid":"gh_7a5c4141778f@app",\r\n"jsonParam":"{\"current_path\":\"home/pages/index.html\",\"current_title\":\"\",\"image_url\":\"https://ut-static.udache.com/webx/mini-pics/U7mDFxU2yh-2-r1BJ-J0X.png\",\"scene\":1001,\"scene_note\":\"\",\"sessionId\":\"SessionId@1672284921_1#1692848476899\"}",\r\n"headImgUrl":"http://mmbiz.qpic.cn/sz_mmbiz_png/9n47wQlh4dH8afD9dQ9uQicibRm5mYz3lawXCLMjmnzFicribH51qsFYxjzPEcTGHGmgX4lkAkQ3jznia8UDEtqsX1w/640?wx_fmt=png&wxfrom=200",\r\n"mainImg":"C:\\wxid_123123\\Applet\\wxaf35009675aa0b2a\\temp\\2.png",\r\n"indexPage":"pages/index/index.html"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/sendApplet",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"sendApplet"
]
}
},
"response": [
{
"name": "发送小程序",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n"wxid":"filehelper",\r\n"waidConcat":"wxaf35009675aa0b2a_118",\r\n"waid":"wxaf35009675aa0b2a",\r\n"appletWxid":"gh_7a5c4141778f@app",\r\n"jsonParam":"{\"current_path\":\"home/pages/index.html\",\"current_title\":\"\",\"image_url\":\"https://ut-static.udache.com/webx/mini-pics/U7mDFxU2yh-2-r1BJ-J0X.png\",\"scene\":1001,\"scene_note\":\"\",\"sessionId\":\"SessionId@1672284921_1#1692848476899\"}",\r\n"headImgUrl":"http://mmbiz.qpic.cn/sz_mmbiz_png/9n47wQlh4dH8afD9dQ9uQicibRm5mYz3lawXCLMjmnzFicribH51qsFYxjzPEcTGHGmgX4lkAkQ3jznia8UDEtqsX1w/640?wx_fmt=png&wxfrom=200",\r\n"mainImg":"C:\\wxid_123123\\Applet\\wxaf35009675aa0b2a\\temp\\2.png",\r\n"indexPage":"pages/index/index.html"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/sendApplet",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"sendApplet"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "发送拍一拍",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "wxid": "wxid_123",\r\n  "receiver": "wxid_123"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/sendPatMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"sendPatMsg"
]
}
},
"response": [
{
"name": "发送拍一拍",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "wxid": "wxid_123",\r\n  "receiver": "wxid_123"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/sendPatMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"sendPatMsg"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "撤回消息",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "msgId":"123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/revokeMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"revokeMsg"
]
}
},
"response": [
{
"name": "撤回消息",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "msgId":"123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/revokeMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"revokeMsg"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "发送名片",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "wxid": "wxid_123",\r\n  "targetId": "wxid_123"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/sendCardMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"sendCardMsg"
]
}
},
"response": [
{
"name": "发送名片",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "wxid": "wxid_123",\r\n  "targetId": "wxid_123"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/sendCardMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"sendCardMsg"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "置顶消息",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "msgId":"123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/topMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"topMsg"
]
}
},
"response": [
{
"name": "置顶消息",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "msgId":"123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/topMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"topMsg"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "取消置顶",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "msgId": "123",\r\n  "chatRoomId": "123@chatroom"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/removeTopMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"removeTopMsg"
]
}
},
"response": [
{
"name": "取消置顶",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "msgId": "123",\r\n  "chatRoomId": "123@chatroom"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/removeTopMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"removeTopMsg"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "转发消息",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "msgId": "123",\r\n  "wxid": "wxid_123"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/forwardMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"forwardMsg"
]
}
},
"response": [
{
"name": "转发消息",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "msgId": "123",\r\n  "wxid": "wxid_123"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/forwardMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"forwardMsg"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "发送链接",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "wxid":"wxid_123",\r\n    "title":"baidu",\r\n    "url":"https://baidu.com",\r\n    "thumbUrl":"https://mmbiz.qpic.cn/sz__jpg/tpzwaqMCicQyEkpxmpmmP9KgoBHiciamYhqZ0ff4kNlozxgRq4AtEzibo4iaw/640?wxtype=jpeg&wxfrom=0",\r\n    "digest":"百度一下，什么都知道"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/sendLinkMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"sendLinkMsg"
]
}
},
"response": [
{
"name": "发送链接",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "wxid":"wxid_123",\r\n    "title":"baidu",\r\n    "url":"https://baidu.com",\r\n    "thumbUrl":"https://mmbiz.qpic.cn/sz__jpg/tpzwaqMCicQyEkpxmpmmP9KgoBHiciamYhqZ0ff4kNlozxgRq4AtEzibo4iaw/640?wxtype=jpeg&wxfrom=0",\r\n    "digest":"百度一下，什么都知道"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/sendLinkMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"sendLinkMsg"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "获取聊天记录",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "wxid":"wxid_4643316432811",\r\n    "startDate":"2024-08-24",\r\n    "endDate":"2024-09-24",\r\n    "dateType":2\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/getMsgList",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"getMsgList"
]
}
},
"response": [
{
"name": "发送链接",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "wxid":"wxid_123",\r\n    "title":"baidu",\r\n    "url":"https://baidu.com",\r\n    "thumbUrl":"https://mmbiz.qpic.cn/sz__jpg/tpzwaqMCicQyEkpxmpmmP9KgoBHiciamYhqZ0ff4kNlozxgRq4AtEzibo4iaw/640?wxtype=jpeg&wxfrom=0",\r\n    "digest":"百度一下，什么都知道"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/sendLinkMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"sendLinkMsg"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "获取聊天记录分页",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "limit":10,\r\n    "isSortDesc":true,\r\n    "wxid":"9948090198@chatroom"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/getMsgListByPage",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"getMsgListByPage"
]
}
},
"response": [
{
"name": "发送链接",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "wxid":"wxid_123",\r\n    "title":"baidu",\r\n    "url":"https://baidu.com",\r\n    "thumbUrl":"https://mmbiz.qpic.cn/sz__jpg/tpzwaqMCicQyEkpxmpmmP9KgoBHiciamYhqZ0ff4kNlozxgRq4AtEzibo4iaw/640?wxtype=jpeg&wxfrom=0",\r\n    "digest":"百度一下，什么都知道"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/msg/sendLinkMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"msg",
"sendLinkMsg"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
}
]
},
{
"name": "Other",
"item": [
{
"name": "添加到收藏",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "msgId":"123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/other/addFavFromMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"other",
"addFavFromMsg"
]
}
},
"response": [
{
"name": "添加到收藏",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "msgId":"123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/other/addFavFromMsg",
"host": [
"{{HOST}}"
],
"path": [
"api",
"other",
"addFavFromMsg"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "收藏图片",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "msgId":"123",\r\n    "imagePath":"C:\\1.png"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/other/addFavFromImage",
"host": [
"{{HOST}}"
],
"path": [
"api",
"other",
"addFavFromImage"
]
}
},
"response": [
{
"name": "收藏图片",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "msgId":"123",\r\n    "imagePath":"C:\\1.png"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/other/addFavFromImage",
"host": [
"{{HOST}}"
],
"path": [
"api",
"other",
"addFavFromImage"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "下载附件（暂时无效，等待修复）",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "msgId":"7755058781297840811",\r\n    "storeDir":"C:\\Users\\<USER>\\Desktop"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/other/downloadAttach",
"host": [
"{{HOST}}"
],
"path": [
"api",
"other",
"downloadAttach"
]
}
},
"response": [
{
"name": "下载附件",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "msgId":"123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/other/downloadAttach",
"host": [
"{{HOST}}"
],
"path": [
"api",
"other",
"downloadAttach"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "下载图片和视频",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "fileId": "3057020100044b30490201000204d077751302032df7fa02042decd476020466f5ad17042435643938336362632d653338362d343030652d613139622d383365323563333730393034020405190a020201000405004c51e600",\r\n    "aesKey": "2414940217f9a377379cb77909fdd753",\r\n    "msgId": "8923908884835653068",\r\n    "fileType": "image",\r\n    "savePath": "C:\\Users\\<USER>\\Desktop\\8923908884835653068.jpg"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/other/downloadMedia",
"host": [
"{{HOST}}"
],
"path": [
"api",
"other",
"downloadMedia"
]
}
},
"response": [
{
"name": "下载附件",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "msgId":"123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/other/downloadAttach",
"host": [
"{{HOST}}"
],
"path": [
"api",
"other",
"downloadAttach"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "解码图片",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "filePath":"C:\\Users\\<USER>\\Desktop\\response.dat",\r\n    "storeDir":"C:\\Users\\<USER>\\Desktop\\"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/other/decodeImage",
"host": [
"{{HOST}}"
],
"path": [
"api",
"other",
"decodeImage"
]
}
},
"response": [
{
"name": "解码图片",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "filePath":"C:\\img\\1.dat",\r\n    "storeDir":"C:\\img"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/other/decodeImage",
"host": [
"{{HOST}}"
],
"path": [
"api",
"other",
"decodeImage"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "解码朋友圈媒体",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "filePath":"C:\\Users\\<USER>\\Desktop\\response.jpeg_dat",\r\n    "storePath":"C:\\Users\\<USER>\\Desktop\\response.jpeg",\r\n    "key":"4846897661519304521"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/other/decodeSnsMedia",
"host": [
"{{HOST}}"
],
"path": [
"api",
"other",
"decodeSnsMedia"
]
}
},
"response": [
{
"name": "解码图片",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "filePath":"C:\\img\\1.dat",\r\n    "storeDir":"C:\\img"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/other/decodeImage",
"host": [
"{{HOST}}"
],
"path": [
"api",
"other",
"decodeImage"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "获取语音",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "msgId":"123",\r\n    "storeDir":"C:\\voice"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/other/getVoiceByMsgId",
"host": [
"{{HOST}}"
],
"path": [
"api",
"other",
"getVoiceByMsgId"
]
}
},
"response": [
{
"name": "获取语音",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "msgId":"123",\r\n    "storeDir":"C:\\voice"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/other/getVoiceByMsgId",
"host": [
"{{HOST}}"
],
"path": [
"api",
"other",
"getVoiceByMsgId"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "ocr识别",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "imagePath":"C:\\1.jpg"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/other/ocr",
"host": [
"{{HOST}}"
],
"path": [
"api",
"other",
"ocr"
]
}
},
"response": [
{
"name": "ocr识别",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "imagePath":"C:\\1.jpg"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/other/ocr",
"host": [
"{{HOST}}"
],
"path": [
"api",
"other",
"ocr"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "数据库解密",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "sourcePath": "C:\\Users\\<USER>\\Documents\\WeChat Files\\wxid_nx6u6i10zukq22\\Msg\\MicroMsg.db",\r\n  "destPath": "C:\\Users\\<USER>\\Desktop\\db_decrypt\\MicroMsg.db",\r\n  "dbkey": "7f8297080425424ab35a47edea0fce6ae72ddf52a4144bce983c2da10ec94326"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/other/decodeSqlite",
"host": [
"{{HOST}}"
],
"path": [
"api",
"other",
"decodeSqlite"
]
}
},
"response": [
{
"name": "数据库解密",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "sourcePath": "C:\\Sns.db",\r\n  "destPath": "C:\\Decrypt\\Sns.db",\r\n  "dbkey": "7f8297080425424ab35a47edea0fce6ae72ddf52a4144bce983c2da10ec94326"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/other/decodeSqlite",
"host": [
"{{HOST}}"
],
"path": [
"api",
"other",
"decodeSqlite"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "获取主窗体句柄",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/other/getMainHwnd",
"host": [
"{{HOST}}"
],
"path": [
"api",
"other",
"getMainHwnd"
]
}
},
"response": [
{
"name": "获取主窗体句柄",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/other/getMainHwnd",
"host": [
"{{HOST}}"
],
"path": [
"api",
"other",
"getMainHwnd"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "获取主窗体句柄V2",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/other/getMainHwndV2",
"host": [
"{{HOST}}"
],
"path": [
"api",
"other",
"getMainHwndV2"
]
}
},
"response": [
{
"name": "获取主窗体句柄",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/other/getMainHwnd",
"host": [
"{{HOST}}"
],
"path": [
"api",
"other",
"getMainHwnd"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
}
]
},
{
"name": "Pay",
"item": [
{
"name": "收款",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "wxid":"wxid_123",\r\n    "transferid":"10000500012023020619112332136412"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/pay/getPayTransfer",
"host": [
"{{HOST}}"
],
"path": [
"api",
"pay",
"getPayTransfer"
]
}
},
"response": [
{
"name": "收款",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "wxid":"wxid_123",\r\n    "transferid":"10000500012023020619112332136412"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/pay/getPayTransfer",
"host": [
"{{HOST}}"
],
"path": [
"api",
"pay",
"getPayTransfer"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
}
]
},
{
"name": "Session",
"item": [
{
"name": "置顶会话",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "sessionId":"wxid_123",\r\n    "top":1\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/session/topSession",
"host": [
"{{HOST}}"
],
"path": [
"api",
"session",
"topSession"
]
}
},
"response": [
{
"name": "置顶会话",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "sessionId":"wxid_123",\r\n    "top":1\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/session/topSession",
"host": [
"{{HOST}}"
],
"path": [
"api",
"session",
"topSession"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "免打扰",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "sessionId":"wxid_123",\r\n    "enable":1\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/session/mute",
"host": [
"{{HOST}}"
],
"path": [
"api",
"session",
"mute"
]
}
},
"response": [
{
"name": "免打扰",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "sessionId":"wxid_123",\r\n    "enable":1\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/session/mute",
"host": [
"{{HOST}}"
],
"path": [
"api",
"session",
"mute"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "添加到通讯录",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "sessionId":"wxid_123",\r\n    "delete":0\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/session/addToContact",
"host": [
"{{HOST}}"
],
"path": [
"api",
"session",
"addToContact"
]
}
},
"response": [
{
"name": "添加到通讯录",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "sessionId":"wxid_123",\r\n    "delete":0\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/session/addToContact",
"host": [
"{{HOST}}"
],
"path": [
"api",
"session",
"addToContact"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "是否置顶会话",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "sessionId":"wxid_123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/session/isTopSession",
"host": [
"{{HOST}}"
],
"path": [
"api",
"session",
"isTopSession"
]
}
},
"response": [
{
"name": "是否置顶会话",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "sessionId":"wxid_123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/session/isTopSession",
"host": [
"{{HOST}}"
],
"path": [
"api",
"session",
"isTopSession"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "是否免打扰",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "sessionId":"wxid_123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/session/isMute",
"host": [
"{{HOST}}"
],
"path": [
"api",
"session",
"isMute"
]
}
},
"response": [
{
"name": "是否免打扰",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "sessionId":"wxid_123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/session/isMute",
"host": [
"{{HOST}}"
],
"path": [
"api",
"session",
"isMute"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "设置为已读",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "wxid":"wxid_123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/session/setSessionReaded",
"host": [
"{{HOST}}"
],
"path": [
"api",
"session",
"setSessionReaded"
]
}
},
"response": [
{
"name": "设置为已读",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "wxid":"wxid_123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/session/setSessionReaded",
"host": [
"{{HOST}}"
],
"path": [
"api",
"session",
"setSessionReaded"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "获取会话列表",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/session/getSessionList",
"host": [
"{{HOST}}"
],
"path": [
"api",
"session",
"getSessionList"
]
}
},
"response": [
{
"name": "获取会话列表",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/session/getSessionList",
"host": [
"{{HOST}}"
],
"path": [
"api",
"session",
"getSessionList"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
}
]
},
{
"name": "Sns",
"item": [
{
"name": "朋友圈首页",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/sns/getSNSFirstPage",
"host": [
"{{HOST}}"
],
"path": [
"api",
"sns",
"getSNSFirstPage"
]
}
},
"response": []
},
{
"name": "朋友圈下一页",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "snsId":"14473879396511593025"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/sns/getSNSNextPage",
"host": [
"{{HOST}}"
],
"path": [
"api",
"sns",
"getSNSNextPage"
]
}
},
"response": [
{
"name": "朋友圈下一页",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "snsId":"14443678082776576586"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/sns/getSNSNextPage",
"host": [
"{{HOST}}"
],
"path": [
"api",
"sns",
"getSNSNextPage"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
}
]
},
{
"name": "User",
"item": [
{
"name": "检查登录状态",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/user/checkLogin",
"host": [
"{{HOST}}"
],
"path": [
"api",
"user",
"checkLogin"
]
}
},
"response": []
},
{
"name": "当前账号信息",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/user/userInfo",
"host": [
"{{HOST}}"
],
"path": [
"api",
"user",
"userInfo"
]
}
},
"response": []
},
{
"name": "通过添加好友",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "encryptusername":"",\r\n    "ticket":"",\r\n    "scene":1\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/user/doVerifyUser",
"host": [
"{{HOST}}"
],
"path": [
"api",
"user",
"doVerifyUser"
]
}
},
"response": [
{
"name": "通过添加好友",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "encryptusername":"",\r\n    "ticket":"",\r\n    "scene":1\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/user/doVerifyUser",
"host": [
"{{HOST}}"
],
"path": [
"api",
"user",
"doVerifyUser"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "更改备注",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "wxid":"wxid_123",\r\n    "remark":"remark"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/user/changeRemark",
"host": [
"{{HOST}}"
],
"path": [
"api",
"user",
"changeRemark"
]
}
},
"response": [
{
"name": "更改备注",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "wxid":"wxid_123",\r\n    "remark":"remark"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/user/changeRemark",
"host": [
"{{HOST}}"
],
"path": [
"api",
"user",
"changeRemark"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "删除好友",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "wxid":"wxid_123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/user/deleteFriend",
"host": [
"{{HOST}}"
],
"path": [
"api",
"user",
"deleteFriend"
]
}
},
"response": [
{
"name": "删除好友",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "wxid":"wxid_123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/user/deleteFriend",
"host": [
"{{HOST}}"
],
"path": [
"api",
"user",
"deleteFriend"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "是否为好友",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "wxid":"wxid_1dme58g6h9mi22"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/user/getContact",
"host": [
"{{HOST}}"
],
"path": [
"api",
"user",
"getContact"
]
}
},
"response": [
{
"name": "是否为好友",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "wxid":"wxid_123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/user/isFriend",
"host": [
"{{HOST}}"
],
"path": [
"api",
"user",
"isFriend"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "联系人信息",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "wxid":"wxid_ccun37bujro22",\r\n    "useCache":true\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/user/getContactProfile",
"host": [
"{{HOST}}"
],
"path": [
"api",
"user",
"getContactProfile"
]
}
},
"response": [
{
"name": "联系人信息",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "wxid":"wxid_123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/user/getContactProfile",
"host": [
"{{HOST}}"
],
"path": [
"api",
"user",
"getContactProfile"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "所有标签",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/user/getContactLabelList",
"host": [
"{{HOST}}"
],
"path": [
"api",
"user",
"getContactLabelList"
]
}
},
"response": [
{
"name": "所有标签",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/user/getContactLabelList",
"host": [
"{{HOST}}"
],
"path": [
"api",
"user",
"getContactLabelList"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "联系人标签",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "wxid":"wxid_123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/user/getContactLabel",
"host": [
"{{HOST}}"
],
"path": [
"api",
"user",
"getContactLabel"
]
}
},
"response": [
{
"name": "联系人标签",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "wxid":"wxid_123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/user/getContactLabel",
"host": [
"{{HOST}}"
],
"path": [
"api",
"user",
"getContactLabel"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "获取用户头像",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "wxid":"wxid_123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/user/getHeadImgUrlByUserName",
"host": [
"{{HOST}}"
],
"path": [
"api",
"user",
"getHeadImgUrlByUserName"
]
}
},
"response": [
{
"name": "获取用户头像",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n    "wxid":"wxid_123"\r\n}",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/user/getHeadImgUrlByUserName",
"host": [
"{{HOST}}"
],
"path": [
"api",
"user",
"getHeadImgUrlByUserName"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
},
{
"name": "联系人列表",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/user/getContactList",
"host": [
"{{HOST}}"
],
"path": [
"api",
"user",
"getContactList"
]
}
},
"response": []
},
{
"name": "加群好友",
"request": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "wxid": "wxid_123",\r\n  "chatRoomId": "123@chatroom",\r\n  "text": "Hi~"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/user/addFriendFromGroup",
"host": [
"{{HOST}}"
],
"path": [
"api",
"user",
"addFriendFromGroup"
]
}
},
"response": [
{
"name": "加群好友",
"originalRequest": {
"method": "POST",
"header": [
{
"key": "X-WeChat-PID",
"value": "{{WECHAT_PID}}",
"type": "text"
},
{
"key": "",
"value": "",
"type": "text"
}
],
"body": {
"mode": "raw",
"raw": "{\r\n  "wxid": "wxid_123",\r\n  "chatRoomId": "123@chatroom",\r\n  "text": "Hi~"\r\n}\r\n",
"options": {
"raw": {
"language": "json"
}
}
},
"url": {
"raw": "{{HOST}}/api/user/addFriendFromGroup",
"host": [
"{{HOST}}"
],
"path": [
"api",
"user",
"addFriendFromGroup"
]
}
},
"_postman_previewlanguage": null,
"header": null,
"cookie": [],
"body": null
}
]
}
]
}
],
"event": [
{
"listen": "prerequest",
"script": {
"type": "text/javascript",
"packages": {},
"exec": [
""
]
}
},
{
"listen": "test",
"script": {
"type": "text/javascript",
"packages": {},
"exec": [
""
]
}
}
],
"variable": [
{
"key": "HOST",
"value": "http://127.0.0.1:50007",
"type": "string"
},
{
"key": "WECHAT_PID",
"value": ""
}
]
}