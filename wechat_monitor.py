#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信群消息监控程序 - 正式版
实现实时监控微信群消息，输出JSON格式的完整消息信息
"""

import requests
import json
import time
import datetime
import re
import base64
import sys
from config import *

class WeChatMonitor:
    def __init__(self):
        self.rpc_host = RPC_HOST
        self.wechat_pid = WECHAT_PID
        self.headers = {
            "X-WeChat-PID": self.wechat_pid,
            "Content-Type": "application/json"
        }
        self.db_handles = {}
        self.contacts_cache = {}
        self.last_msg_id = 0
        self.debug_mode = DEBUG_MODE
        
    def extract_sender_wxid(self, bytes_extra):
        """从BytesExtra中提取发送者微信ID"""
        if not bytes_extra:
            return None
        
        try:
            bytes_str = str(bytes_extra)
            
            # 尝试base64解码
            try:
                decoded = base64.b64decode(bytes_str).decode('utf-8', errors='ignore')
                wxid_pattern = r'wxid_[a-zA-Z0-9]+'
                match = re.search(wxid_pattern, decoded)
                if match:
                    return match.group(0)
            except:
                pass
            
            # 直接查找
            wxid_pattern = r'wxid_[a-zA-Z0-9]+'
            match = re.search(wxid_pattern, bytes_str)
            if match:
                return match.group(0)
            
            return None
        except:
            return None

    def is_quote_message(self, msg):
        """判断是否为引用消息"""
        return (
            str(msg.get("type")) == "49" and 
            str(msg.get("subType")) in ["51", "57"] and 
            msg.get("compressContent") is not None
        )

    def parse_quote_info(self, compress_content):
        """解析引用消息信息 - 只提取MsgSvrID"""
        if not compress_content:
            return None
        
        try:
            # base64解码
            decoded = base64.b64decode(compress_content).decode('utf-8', errors='ignore')
            
            # 只提取被引用消息的服务器ID (MsgSvrID)
            svrid_matches = re.findall(r'\b(\d{15,20})\b', decoded)
            if svrid_matches:
                return {
                    "被引用消息ID": svrid_matches[0]
                }
            
            return None
            
        except Exception as e:
            return None

    def get_db_info(self):
        """获取数据库信息"""
        try:
            response = requests.post(
                f"{self.rpc_host}/api/db/getDBInfo",
                headers=self.headers,
                json={}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("Code") == 1:
                    db_info = data.get("Data", [])
                    for db in db_info:
                        db_name = db.get("databaseName", "")
                        if "MSG0.db" in db_name:
                            self.db_handles["MSG"] = db.get("handle")
                        elif "MicroMsg.db" in db_name:
                            self.db_handles["MicroMsg"] = db.get("handle")
                    
                    print(f"数据库连接成功: MSG={self.db_handles.get('MSG')}, MicroMsg={self.db_handles.get('MicroMsg')}")
                    return True
                else:
                    print(f"获取数据库信息失败: {data.get('Msg', 'Unknown error')}")
                    return False
            else:
                print(f"请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"获取数据库信息异常: {e}")
            return False

    def execute_sql(self, db_handle, sql):
        """执行SQL查询"""
        try:
            response = requests.post(
                f"{self.rpc_host}/api/db/execSql",
                headers=self.headers,
                json={
                    "dbHandle": str(db_handle),
                    "sql": sql
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("Code") == 1:
                    return data.get("Data", [])
                else:
                    print(f"SQL执行失败: {data.get('Msg', 'Unknown error')}")
                    return []
            else:
                print(f"SQL请求失败: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"SQL执行异常: {e}")
            return []

    def load_contacts(self):
        """加载联系人信息到缓存"""
        if not self.db_handles.get("MicroMsg"):
            print("MicroMsg数据库句柄未找到")
            return
        
        # 限制查询数量，避免内存占用过大
        sql = f"SELECT UserName, NickName, Remark FROM Contact LIMIT {MAX_CONTACTS_CACHE};"
        contacts = self.execute_sql(self.db_handles["MicroMsg"], sql)
        
        for contact in contacts:
            username = contact.get("UserName", "")
            nickname = contact.get("NickName", "")
            remark = contact.get("Remark", "")
            
            # 优先级: 备注名 > 昵称 > 微信ID
            display_name = remark if remark else (nickname if nickname else username)
            self.contacts_cache[username] = display_name
        
        print(f"联系人缓存加载完成，共 {len(self.contacts_cache)} 个联系人")

    def get_contact_name(self, wxid):
        """获取联系人显示名称"""
        if wxid in self.contacts_cache:
            return self.contacts_cache[wxid]
        
        # 如果缓存中没有，尝试从数据库查询
        if self.db_handles.get("MicroMsg"):
            sql = f"SELECT NickName, Remark FROM Contact WHERE UserName = '{wxid}' LIMIT 1;"
            result = self.execute_sql(self.db_handles["MicroMsg"], sql)
            if result:
                contact = result[0]
                nickname = contact.get("NickName", "")
                remark = contact.get("Remark", "")
                display_name = remark if remark else (nickname if nickname else wxid)
                self.contacts_cache[wxid] = display_name
                return display_name
        
        return wxid

    def get_group_name(self, group_id):
        """获取群聊名称"""
        return self.get_contact_name(group_id)

    def initialize_monitor(self):
        """初始化监控起点"""
        if not self.db_handles.get("MSG"):
            print("MSG数据库句柄未找到")
            return False
        
        # 获取最新的localId作为监控起点
        sql = "SELECT MAX(localId) as max_id FROM MSG WHERE StrTalker LIKE '%@chatroom';"
        result = self.execute_sql(self.db_handles["MSG"], sql)
        
        if result and result[0].get("max_id"):
            self.last_msg_id = result[0]["max_id"]
            print(f"监控初始化完成，起始消息ID: {self.last_msg_id}")
            return True
        else:
            print("监控初始化失败，无法获取起始消息ID")
            return False

    def monitor_messages(self):
        """实时监控新消息"""
        print("开始监控微信群消息...")
        print("=" * 50)
        
        while True:
            try:
                # 查询新消息
                sql = f"""
                SELECT localId, TalkerId, CreateTime, StrTalker, StrContent, IsSender, Type, SubType, BytesExtra, CompressContent, MsgSvrID
                FROM MSG 
                WHERE localId > {self.last_msg_id}
                AND StrTalker LIKE '%@chatroom'
                ORDER BY localId ASC;
                """
                
                new_messages = self.execute_sql(self.db_handles["MSG"], sql)
                
                for msg in new_messages:
                    self.process_message(msg)
                    self.last_msg_id = msg.get("localId", self.last_msg_id)
                
                # 根据配置的间隔检查
                time.sleep(MONITOR_INTERVAL)
                
            except KeyboardInterrupt:
                print("\n监控已停止")
                break
            except Exception as e:
                print(f"监控异常: {e}")
                time.sleep(5)

    def process_message(self, msg):
        """处理单条消息"""
        try:
            # 基本信息
            local_id = msg.get("localId")
            msg_svr_id = msg.get("MsgSvrID")
            create_time = msg.get("CreateTime")
            str_talker = msg.get("StrTalker", "")
            str_content = msg.get("StrContent", "")
            is_sender = msg.get("IsSender", 0)
            msg_type = msg.get("Type")
            sub_type = msg.get("SubType")
            bytes_extra = msg.get("BytesExtra")
            compress_content = msg.get("CompressContent")
            
            # 跳过自己发送的消息（除非配置允许显示）
            if is_sender == 1 and not SHOW_SELF_MESSAGES:
                return
            
            # 格式化时间
            if create_time:
                formatted_time = datetime.datetime.fromtimestamp(create_time).strftime("%Y-%m-%d %H:%M:%S")
            else:
                formatted_time = "未知时间"
            
            # 获取群聊名称
            group_name = self.get_group_name(str_talker)
            
            # 获取发送者昵称
            sender_wxid = self.extract_sender_wxid(bytes_extra)
            sender_name = self.get_contact_name(sender_wxid) if sender_wxid else "未知发送者"
            
            # 统一使用MsgSvrID作为消息ID
            message_id = str(msg_svr_id) if msg_svr_id and str(msg_svr_id) != "0" else f"local_{local_id}"
            
            # 处理引用信息
            quote_info = {}
            if self.is_quote_message(msg):
                quote_data = self.parse_quote_info(compress_content)
                if quote_data:
                    quote_info = quote_data
            
            # 构建输出消息
            output_msg = {
                "发送人": sender_name,
                "发送时间": formatted_time,
                "群聊名称": group_name,
                "消息内容": str_content,
                "消息类型": str(msg_type),
                "消息ID": message_id,
                "引用信息": quote_info
            }
            
            # 输出JSON格式消息
            print(json.dumps(output_msg, ensure_ascii=False, indent=2))
            print("-" * 30)
            
        except Exception as e:
            print(f"处理消息异常: {e}")

    def run(self):
        """运行监控程序"""
        print("微信群消息监控系统启动中...")
        
        # 1. 获取数据库信息
        if not self.get_db_info():
            print("无法获取数据库信息，程序退出")
            return
        
        # 2. 加载联系人信息
        self.load_contacts()
        
        # 3. 初始化监控起点
        if not self.initialize_monitor():
            print("监控初始化失败，程序退出")
            return
        
        # 4. 开始实时监控
        self.monitor_messages()

def main():
    """主函数"""
    monitor = WeChatMonitor()
    monitor.run()

if __name__ == "__main__":
    main()
