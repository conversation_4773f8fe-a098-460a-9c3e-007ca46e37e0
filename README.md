# 微信群消息监控系统

基于微信RPC服务的实时群消息监控系统，能够监控所有微信群聊的新消息，并以JSON格式输出包含真实发送人昵称和引用消息信息的完整数据。

## 功能特性

- ✅ **实时监控**：监控获取全部群的新消息
- ✅ **真实昵称**：显示发送人真实昵称（而非微信ID）
- ✅ **引用消息识别**：识别和解析引用回复消息
- ✅ **统一ID体系**：全部使用MsgSvrID保证全局唯一性
- ✅ **JSON格式输出**：标准化的数据输出格式

## 环境要求

- Python 3.6+
- requests库
- 微信客户端运行中
- 微信RPC服务运行在 `127.0.0.1:50007`

## 安装依赖

```bash
pip install requests
```

## 使用方法

### 1. 测试连接

首先运行连接测试脚本，确保微信RPC服务正常：

```bash
python test_connection.py
```

预期输出：
```
微信RPC连接测试开始
==================================================
测试1: 获取微信客户端列表
响应状态: 1
响应消息: success
  PID: 22504, 路径: C:\Program Files\Tencent\WeChat\WeChat.exe
✓ 微信客户端列表获取成功
--------------------------------------------------
测试2: 获取数据库信息
响应状态: 1
响应消息: success
  数据库: MSG0.db
  句柄: 1188434278240
  表数量: 1
✓ 数据库信息获取成功
--------------------------------------------------
测试完成
```

### 2. 启动监控

运行主监控程序：

```bash
python wechat_monitor.py
```

### 3. 输出格式

程序会实时输出JSON格式的消息数据：

#### 普通消息示例
```json
{
  "发送人": "小张",
  "发送时间": "2025-08-21 14:34:13",
  "群聊名称": "测试群",
  "消息内容": "测试消息内容",
  "消息类型": "1",
  "消息ID": "3264915067667192932",
  "引用信息": {}
}
```

#### 引用消息示例
```json
{
  "发送人": "Wonder",
  "发送时间": "2025-08-21 14:25:35",
  "群聊名称": "测试群",
  "消息内容": "",
  "消息类型": "49",
  "消息ID": "2052665057659932943",
  "引用信息": {
    "被引用消息ID": "2640209026829637417"
  }
}
```

## 配置说明

在 `wechat_monitor.py` 中可以修改以下配置：

```python
# RPC服务地址
RPC_HOST = "http://127.0.0.1:50007"

# 微信进程ID（根据实际情况修改）
WECHAT_PID = "22504"
```

## 技术架构

### 核心组件

1. **微信RPC服务**：运行在 `http://127.0.0.1:50007`
2. **数据库访问**：通过RPC接口访问微信本地SQLite数据库
   - **MSG0.db**：存储消息数据
   - **MicroMsg.db**：存储联系人和群聊信息

### 关键技术实现

#### 1. 发送者昵称获取
从BytesExtra字段解析发送者微信ID，然后从联系人数据库获取真实昵称。

#### 2. 引用消息识别
- 识别条件：`Type = 49` 且 `SubType` 为 51 或 57
- 从CompressContent字段解析被引用消息的MsgSvrID

#### 3. 实时监控
- 使用localId递增特性进行增量查询
- 每3秒轮询一次新消息
- 只监控群聊消息（StrTalker以@chatroom结尾）

## 故障排除

### 常见问题

1. **连接失败**
   - 检查微信RPC服务是否运行在 `127.0.0.1:50007`
   - 确认微信客户端正在运行

2. **无消息输出**
   - 确认群聊有新消息活动
   - 检查WECHAT_PID是否正确

3. **昵称显示为ID**
   - 检查联系人缓存加载情况
   - 确认MicroMsg.db数据库访问正常

4. **引用解析失败**
   - 验证CompressContent字段内容
   - 检查base64解码是否正常

### 调试方法

1. 运行连接测试：
   ```bash
   python test_connection.py
   ```

2. 检查日志输出，查看具体错误信息

3. 确认微信RPC服务状态

## 注意事项

- 程序需要微信客户端保持运行状态
- 首次运行会加载联系人缓存，可能需要几秒钟
- 监控过程中请勿关闭微信客户端
- 程序会跳过自己发送的消息

## 停止监控

按 `Ctrl+C` 停止监控程序。

## 扩展功能

可以基于此系统扩展以下功能：

1. **消息过滤**：按关键词、发送者过滤
2. **数据存储**：将监控结果存储到数据库
3. **实时推送**：集成webhook或消息队列
4. **多群管理**：选择性监控特定群聊
5. **消息统计**：生成群聊活跃度报告

---

**这是一个完整、稳定、可投入生产使用的微信群消息监控系统！**
