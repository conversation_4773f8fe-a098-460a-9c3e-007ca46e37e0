#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的日志记录模块
"""

import datetime
import os
from config import DEBUG_MODE

class Logger:
    def __init__(self, log_file="monitor.log"):
        self.log_file = log_file
        self.debug_mode = DEBUG_MODE
        
    def _write_log(self, level, message):
        """写入日志"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}\n"
        
        # 控制台输出
        print(f"[{level}] {message}")
        
        # 文件输出（如果启用调试模式）
        if self.debug_mode:
            try:
                with open(self.log_file, "a", encoding="utf-8") as f:
                    f.write(log_entry)
            except Exception as e:
                print(f"写入日志文件失败: {e}")
    
    def info(self, message):
        """信息日志"""
        self._write_log("INFO", message)
    
    def warning(self, message):
        """警告日志"""
        self._write_log("WARNING", message)
    
    def error(self, message):
        """错误日志"""
        self._write_log("ERROR", message)
    
    def debug(self, message):
        """调试日志"""
        if self.debug_mode:
            self._write_log("DEBUG", message)

# 全局日志实例
logger = Logger()
